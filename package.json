{"name": "upace-embed-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "prepare": "husky install", "prebuild": "npm run lint:fix && npm test -- --bail --passWithNoTests"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix", "jest --bail --passWithNoTests"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "babelMacros": {"twin": {"preset": "styled-components"}}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@mobiscroll/react": "^5.30.1", "@propra/react-pdf-table": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf/renderer": "^3.4.2", "@tanstack/react-query": "^5.21.2", "@tanstack/react-query-devtools": "^5.21.3", "@tanstack/react-table": "^8.12.0", "@tanstack/react-virtual": "^3.0.4", "@types/qrcode": "^1.5.5", "add-to-calendar-button-react": "^2.6.10", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.6.0", "firebase": "^11.8.1", "html3pdf": "^0.12.2", "ky": "^1.7.5", "lodash": "^4.17.21", "lucide-react": "^0.331.0", "match-sorter": "^6.4.0", "next": "^14.1.0", "next-client-cookies": "^1.1.1", "next-themes": "^0.3.0", "nuqs": "^2.2.3", "qrcode": "^1.5.3", "qrcode.react": "^3.1.0", "react": "^18", "react-device-detect": "^2.2.3", "react-dom": "^18", "react-hook-form": "^7.51.0", "react-icons": "^5.0.1", "react-select": "^5.8.0", "react-timer-hook": "^3.0.7", "sharp": "^0.33.2", "sonner": "^1.4.3", "styled-components": "^6.1.8", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.3", "vaul": "^0.9.0", "zod": "^3.22.4", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/plugin-syntax-typescript": "^7.23.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.14.202", "@types/next-auth": "^3.15.0", "@types/node": "^20", "@types/react": "^18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^5.60.1", "autoprefixer": "^10.0.1", "babel-loader": "^9.1.3", "babel-plugin-macros": "^3.1.0", "babel-plugin-styled-components": "^2.1.4", "eslint": "^8", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-filename-rules": "^1.3.1", "eslint-plugin-lodash-fp": "^2.2.0-a1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-prefer-function-component": "^3.3.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "lint-staged": "^16.1.0", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.13", "react-copy-to-clipboard": "^5.1.0", "tailwindcss": "^3.3.0", "ts-jest": "^29.3.4", "twin.macro": "^3.4.1", "typescript": "^5"}}