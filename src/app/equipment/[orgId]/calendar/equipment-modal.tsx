import { DoorClosed } from "@/components/custom/icons/door-closed";
import { Swimmer } from "@/components/custom/icons/swimmer";
import { InfoDetailsRow } from "@/components/custom/info-details-rows";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { X } from "lucide-react";
import { useState } from "react";
import { EquipmentDropdowns } from "../modules/equipment-dropdowns";
import ImageCard from "../modules/image-card";
import { SelectedEquipmentSchedule } from "../types";

export const EquipmentModal = ({
  orgId,
  isOpen,
  selected,
  setIsOpen,
}: {
  orgId: string;
  isOpen: boolean;
  selected: SelectedEquipmentSchedule;
  setIsOpen: (value?: boolean) => void;
}) => {
  const [startTime, setStartTime] = useState("1");
  const [duration, setDuration] = useState("1");
  const [peopleCount, setPeopleCount] = useState("1");

  const { equipment, selectedDate } = selected;
  const {
    id: equipmentId,
    name,
    facility,
    type: facilityType,
    durations,
    attending_persons: attendingPersons,
  } = equipment;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className='w-full max-h-[90%] md:max-h-[85%] lg:max-h-[80%] max-w-full md:max-w-[480px] md:rounded-lg border-white overflow-y-auto overflow-x-hidden outline-none px-0 pt-0'>
        <div className='sticky top-0 left-0 right-0 z-50 flex justify-between items-start bg-white/90 p-2 sm:p-3 md:p-4'>
          <DialogTitle className='text-base sm:text-lg md:text-xl font-semibold text-[#009DC4]'></DialogTitle>
          <Button
            onClick={() => setIsOpen(false)}
            size='sm'
            variant='outline'
            className='h-8 md:h-9 text-sm border-none flex gap-2'
          >
            <span className='text-xs sm:text-sm md:text-sm'>Close</span>
            <X className='h-3 w-3 md:h-4 md:w-4' />
          </Button>
        </div>
        <div className='grid h-full gap-3 sm:gap-4 md:gap-4 pt-0 px-3 sm:px-4 md:px-5 overflow-y-auto overflow-x-hidden'>
          <ImageCard selected={selected} />

          <div className='mt-2 sm:mt-3 md:mt-3'>
            <p className='text-[#009DC4] font-bold text-lg md:text-lg mb-2 md:mb-2'>
              {name}
            </p>
            <div className='space-y-2 sm:space-y-3 md:space-y-3'>
              <InfoDetailsRow icon={<DoorClosed className='md:h-5 md:w-5' />}>
                {facility}
              </InfoDetailsRow>
              <InfoDetailsRow icon={<Swimmer className='md:h-5 md:w-5' />}>
                {facilityType}
              </InfoDetailsRow>
            </div>
          </div>

          <p className='text-gray-600 text-sm mb-4'>
            Cancelations must be made 60 minutes prior to start time. Your spot
            will be forfeited if you do not arrive within 10 minutes of the
            start time.
          </p>

          <EquipmentDropdowns
            orgId={orgId}
            equipmentId={equipmentId}
            date={selectedDate}
            startTime={startTime}
            setStartTime={setStartTime}
            duration={duration}
            setDuration={setDuration}
            peopleCount={peopleCount}
            setPeopleCount={setPeopleCount}
            durations={durations}
            attendingPersons={attendingPersons}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
