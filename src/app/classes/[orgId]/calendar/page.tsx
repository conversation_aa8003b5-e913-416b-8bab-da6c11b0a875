import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { ActivityFilters } from "@/components/custom/activity-filters";
import { LoginModal } from "@/components/custom/login-auth/login-modal";

import { Footer } from "@/components/custom/footer";
import { LoginLogoutButton } from "@/components/custom/login-logout-button";
import { ReservationsModal } from "@/components/custom/reservations/reservations-modal";
import { WeekDatePicker } from "@/components/custom/week-date-picker";
import { ClassDownloadPDF } from "@/components/pdf-downloader-module/class-pdf-modal";
import { Fragment } from "react";
import { EmbedPageViewTracker } from "../modules/analytics/EmbedPageViewTracker";
import { ClassCalendarOverview } from "./overview";

export default async function ClassCalendarPage({
  params,
  searchParams,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const {
    facilitiesOptions,
    categoriesOptions,
    appUrls,
    configs,
    clientName,
    clientLogo,
    clientPdfHeader,
    clientBackground,
  } = await fetchClientConfigs(params.orgId);

  return (
    <Fragment>
      <EmbedPageViewTracker pageTitle='Calendar Page' />
      <div className='overflow-hidden mb-20 container'>
        <div className='container rounded-b-none rounded-t-lg border-2 border-white-300 !bg-white flex flex-col mt-4'>
          <LoginLogoutButton />
          <WeekDatePicker />
        </div>
        <ActivityFilters
          facilitiesOptions={facilitiesOptions}
          categoriesOptions={categoriesOptions}
          activityLabel='Activity'
        />
        <LoginModal
          buttonColor={configs?.embed?.button_color}
          appUrls={appUrls}
          configs={configs}
          orgId={params.orgId}
        />

        <ClassCalendarOverview
          searchParams={searchParams}
          orgId={params.orgId as string}
        />

        <ClassDownloadPDF
          facilitiesOptions={facilitiesOptions}
          categoriesOptions={categoriesOptions}
          clientName={clientName}
          clientId={params.orgId}
          clientLogo={clientLogo}
          clientPdfHeader={clientPdfHeader}
          appUrls={appUrls}
          clientBackground={clientBackground}
        />

        <ReservationsModal />
      </div>
      <Footer
        appUrls={appUrls}
        infoText='Access Class Schedules on The Go! Download the app today:'
      />
    </Fragment>
  );
}
