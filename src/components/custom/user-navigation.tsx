"use client";

import * as React from "react";

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";

export function UserNavigationMenu({
  name,
  email,
}: {
  name: string;
  email: string;
}) {
  return (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Hello {name}</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className='grid gap-2 w-[200px]'>
              <li className='row-span-1'>
                <a className='from-muted/50 to-muted flex h-full w-full flex-col justify-end rounded-md bg-linear-to-b p-6 no-underline outline-hidden select-none focus:shadow-md'>
                  <div className='mt-2 mb-2 text-lg font-medium'>{name}</div>
                  <p className='text-muted-foreground text-sm leading-tight'>
                    {email}
                  </p>
                </a>
              </li>
              <li className='row-span-1'>
                <a className='from-muted/50 to-muted flex  w-full flex-col justify-end rounded-md bg-linear-to-b no-underline outline-hidden select-none focus:shadow-md'>
                  <div className='mt-2 mb-2 text-lg font-medium'>Logout</div>
                </a>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
}
