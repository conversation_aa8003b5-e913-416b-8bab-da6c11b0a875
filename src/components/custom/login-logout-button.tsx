"use client";

import { useStoreValue } from "@/app/StoreContext";
import { Button } from "@/components/ui/button";
import { LogIn, LogOut } from "lucide-react";
import { useSession } from "./login-auth/auth-provider";
import { UserNavigationMenu } from "./user-navigation";

export const LoginLogoutButton = ({
  ctaLabel = "Login to make reservations",
}: {
  ctaLabel?: string;
}) => {
  const { data: sessionData, signOut } = useSession();

  const { dispatch } = useStoreValue(state => state.shouldShowLoginModal);

  return sessionData ? (
    <>
      <UserNavigationMenu
        name={`${sessionData.first_name} ${sessionData.last_name}`}
        email={sessionData?.email}
      />
    </>
  ) : (
    <div className='flex items-center justify-center md:justify-end space-x-4 mt-4 w-full'>
      <Button
        onClick={() => dispatch(() => ({ shouldShowLoginModal: true }))}
        className='text-white'
      >
        <LogIn className='mr-2 h-4 w-4' />
        <span>{ctaLabel}</span>
      </Button>
    </div>
  );
};
