"use client";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { User_Role } from "@/common/types";
import { FirebaseEvent, logEvent } from "@/lib/firebase/config";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { signOut } from "./auth-provider";

export interface LoginUserResponse {
  success: string;
  message: string;
  token?: string;
  university_id?: string;
  first_name?: string;
  last_name?: string;
  name?: string;
}

const generalLogin = async ({
  email,
  password,
}: {
  email: string;
  password: string;
}) =>
  await fetch(`${BASE_API_URL_CLIENT}/auth/login/legacy`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email, password }),
  });

const salesforceLogin = async ({
  username,
  password,
  orgId,
}: {
  username: string;
  password: string;
  orgId?: string;
}) =>
  await fetch(`${BASE_API_URL_CLIENT}/integration/custom`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      username,
      password,
      identifier: "ynorth_sf_sso", // We will make it passed later
      university_id: orgId,
    }),
  });

export const login = async ({
  email,
  password,
  isSalesForceLogin,
  orgId,
}: {
  email: string;
  password: string;
  orgId?: string;
  isSalesForceLogin?: boolean;
}): Promise<LoginUserResponse> => {
  try {
    logEvent(FirebaseEvent.USER_LOGIN_ATTEMPT, { email });

    const response = isSalesForceLogin
      ? await salesforceLogin({ username: email, password, orgId })
      : await generalLogin({ email, password });

    if (!response.ok) {
      throw new Error("Your email or password is invalid");
    }

    const result = await response.json();

    if (result.primary_role !== User_Role.USER) {
      return {
        success: "false",
        message:
          "Access denied: Instructors are not permitted to log in as members.",
      };
    }

    logEvent(FirebaseEvent.USER_LOGIN_SUCCESS, { email });

    return {
      ...result,
      ...result.data,
    };
  } catch (error) {
    logEvent(FirebaseEvent.USER_LOGIN_FAILED, { email });
    signOut();
    throw new Error("Your email or password is invalid");
  }
};

export const useLoginQuery = (onSuccess?: (data?: LoginUserResponse) => void) =>
  useMutation({
    mutationFn: login,
    onSuccess: (data, reqData) => {
      if (data.success === "false") {
        toast.error(data.message);
        throw new Error(data.message);
      }

      //Remember: This is a workaround to support legacy login methods
      if (data?.university_id != reqData?.orgId) {
        toast.error(
          "Sorry, you do not not have account associated with this organization"
        );
        throw new Error(
          "Sorry, you do not not have account associated with this organization"
        );
      }
      onSuccess?.(data);
    },
  });
