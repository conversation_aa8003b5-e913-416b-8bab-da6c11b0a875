"use client";

import { BaseSelect, Option } from "@/components/ui/select";
import { useSearchParams } from "next/navigation";
import { useMemo, useState } from "react";

import { Button } from "@/components/ui/button";
import { useApplyStateToUrl } from "../../common/hooks/useApplyStateToUrl";

import { useStoreValue } from "@/app/StoreContext";
import { IoFilterSharp } from "react-icons/io5";

export const ALL_OPTION = "all";

export const ActivityFilters = ({
  categoriesOptions = [],
  facilitiesOptions = [],
  activityName = "class_category_id",
  activityLabel = "I want to",
  filterByTimes = true,
}: {
  categoriesOptions: Option[];
  facilitiesOptions: Option[];
  activityName?: string;
  activityLabel?: string;
  filterByTimes?: boolean;
}) => {
  const searchParams = useSearchParams();

  const applyToUrl = useApplyStateToUrl();

  const { state: startTimeOptions } = useStoreValue(
    state => state.startTimeOptions
  );

  const { state: endTimes } = useStoreValue(state => state.endTimeOptions);

  const handleChange = (option: Option | Option[], identify: string) => {
    if (Array.isArray(option)) {
      if (option.some(opt => opt.value === ALL_OPTION)) {
        applyToUrl({ [identify]: null });
        return;
      }
      return applyToUrl({ [identify]: option.map(val => val.value).join(",") });
    } else {
      if (option?.value === ALL_OPTION) {
        applyToUrl({ [identify]: null });
        return;
      }
      return applyToUrl({ [identify]: option?.value });
    }
  };

  const startTime = searchParams.get("class_start_time");

  const endTimeOptions = useMemo(
    () =>
      endTimes?.map((option: Option) => ({
        ...option,
        isDisabled: Boolean(startTime && (option.value as string) < startTime),
      })),

    [endTimes, startTime]
  );

  const [showFilters, setShowFilters] = useState(false);

  const gymSelected = useSearchParams().get("gym_id")?.split(",");

  return (
    <>
      <div className='flex justify-center'>
        <Button
          className='lg:hidden mb-4 mt-8 ml-2 text-center text-sm'
          onClick={() => setShowFilters(!showFilters)}
        >
          <IoFilterSharp className='mr-2' />
          {showFilters ? "Hide Filters" : "Show Filters"}
        </Button>
      </div>
      <div
        className={`container justify-center rounded-t-none border-2 p-6 mb-10 grid grid-cols-2 border-t-0  gap-5 lg:flex ${showFilters ? "" : "hidden lg:flex"}`}
      >
        <div className='flex flex-col lg:flex-row lg-flex-grow lg:items-center gap-4'>
          <p className='font-bold'>{activityLabel}</p>
          <BaseSelect
            className='w-full lg:w-[200px]'
            onChange={val => handleChange(val, activityName)}
            placeholder='Choose Activity'
            options={categoriesOptions}
            value={searchParams.get(activityName) ?? ALL_OPTION}
          />
        </div>
        <div className='flex flex-col lg:flex-row lg-flex-grow lg:items-center gap-4'>
          <p className='font-bold'>at</p>
          <BaseSelect
            className='w-full lg:w-[200px]'
            onChange={val => {
              handleChange(val, "gym_id");
            }}
            placeholder='All Locations'
            options={facilitiesOptions}
            value={gymSelected ?? ""}
            multiple
          />
        </div>
        {filterByTimes && (
          <>
            <div className='flex flex-col lg:flex-row lg-flex-grow lg:items-center gap-4'>
              <p className='font-bold'>between</p>
              <BaseSelect
                className='w-full lg:w-[200px]'
                placeholder='Start time'
                options={startTimeOptions}
                onChange={val => handleChange(val, "class_start_time")}
                value={searchParams.get("class_start_time") ?? ""}
              />
            </div>
            <div className='flex flex-col lg:flex-row lg-flex-grow lg:items-center gap-4'>
              <p className='font-bold'>to</p>
              <BaseSelect
                className='w-full lg:w-[200px]'
                placeholder='End time'
                options={endTimeOptions}
                onChange={val => handleChange(val, "class_end_time")}
                value={searchParams.get("class_end_time") ?? ""}
              />
            </div>
          </>
        )}
      </div>
    </>
  );
};
